'use client';

import { useState, useEffect, useRef } from 'react';
import { useAuth } from '../../contexts/AuthContext';

enum LoginStep {
  PHONE_EMAIL = 'phone_email',
  OTP_VERIFICATION = 'otp_verification'
}

interface LoginModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface FormErrors {
  phoneOrEmail?: string;
  otp?: string;
  general?: string;
}

interface SocialLoginProvider {
  id: string;
  name: string;
  icon: string;
  color: string;
}

interface CountryCode {
  name: string;
  dial_code: string;
  code: string;
}

export default function LoginModal({ isOpen, onClose }: LoginModalProps) {
  const { login, verifyOTP, socialLogin } = useAuth();
  const [currentStep, setCurrentStep] = useState<LoginStep>(LoginStep.PHONE_EMAIL);
  const [phoneOrEmail, setPhoneOrEmail] = useState('');
  const [otp, setOtp] = useState('');
  const [errors, setErrors] = useState<FormErrors>({});
  const [countdown, setCountdown] = useState(0);
  const [inputType, setInputType] = useState<'phone' | 'email' | 'default'>('default');
  const [countryCode, setCountryCode] = useState('+1');
  const [isUserTyping, setIsUserTyping] = useState(false);
  const [countryCodes, setCountryCodes] = useState<CountryCode[]>([]);
  const [isLoadingCountries, setIsLoadingCountries] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const phoneInputRef = useRef<HTMLInputElement>(null);
  const emailInputRef = useRef<HTMLInputElement>(null);
  const defaultInputRef = useRef<HTMLInputElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Add custom styles for the country code dropdown
  useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      .country-code-select {
        appearance: menulist !important;
        -webkit-appearance: menulist !important;
        -moz-appearance: menulist !important;
      }

      .country-code-select option {
        padding: 8px 12px;
        font-size: 14px;
      }
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  // Social login providers
  const socialProviders: SocialLoginProvider[] = [
    {
      id: 'google',
      name: 'Google',
      icon: 'ri-google-fill',
      color: 'bg-red-500 hover:bg-red-600'
    },
    {
      id: 'facebook',
      name: 'Facebook',
      icon: 'ri-facebook-fill',
      color: 'bg-blue-600 hover:bg-blue-700'
    }
  ];

  // Fetch country codes from JSON file
  useEffect(() => {
    const fetchCountryCodes = async () => {
      try {
        setIsLoadingCountries(true);
        const response = await fetch('/assets/json/CountryCodes.json');
        if (!response.ok) {
          throw new Error('Failed to fetch country codes');
        }
        const data = await response.json();

        // Show only the 7 most popular countries
        const popularCountries = ['US', 'IN', 'GB', 'CA', 'AU', 'DE', 'FR'];
        const filteredData = data.filter((country: CountryCode) =>
          popularCountries.includes(country.code)
        );

        const sortedData = filteredData.sort((a: CountryCode, b: CountryCode) => {
          const aIndex = popularCountries.indexOf(a.code);
          const bIndex = popularCountries.indexOf(b.code);
          return aIndex - bIndex;
        });

        setCountryCodes(sortedData);
      } catch (error) {
        console.error('Error fetching country codes:', error);
        // Fallback to the same 7 popular countries if fetch fails
        setCountryCodes([
          { name: "United States", dial_code: "+1", code: "US" },
          { name: "India", dial_code: "+91", code: "IN" },
          { name: "United Kingdom", dial_code: "+44", code: "GB" },
          { name: "Canada", dial_code: "+1", code: "CA" },
          { name: "Australia", dial_code: "+61", code: "AU" },
          { name: "Germany", dial_code: "+49", code: "DE" },
          { name: "France", dial_code: "+33", code: "FR" }
        ]);
      } finally {
        setIsLoadingCountries(false);
      }
    };

    fetchCountryCodes();
  }, []);

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setCurrentStep(LoginStep.PHONE_EMAIL);
      setPhoneOrEmail('');
      setOtp('');
      setErrors({});
      setCountdown(0);
      setInputType('default');
      setCountryCode('+1');
      setIsUserTyping(false);
    }

    // Cleanup timeout on unmount
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, [isOpen]);

  // Countdown timer for OTP resend
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (countdown > 0) {
      timer = setTimeout(() => setCountdown(countdown - 1), 1000);
    }
    return () => clearTimeout(timer);
  }, [countdown]);

  // Focus management when input type changes
  useEffect(() => {
    // Only manage focus if user is not actively typing
    if (!isUserTyping) {
      // Small delay to ensure the DOM has updated
      setTimeout(() => {
        if (inputType === 'phone' && phoneInputRef.current) {
          phoneInputRef.current.focus();
        } else if (inputType === 'email' && emailInputRef.current) {
          emailInputRef.current.focus();
        } else if (inputType === 'default' && defaultInputRef.current) {
          defaultInputRef.current.focus();
        }
      }, 50);
    }
  }, [inputType, isUserTyping]);

  // Function to detect input type after 4 characters
  const detectInputType = (value: string): 'phone' | 'email' | 'default' => {
    if (value.length < 4) return 'default';

    // Trim whitespace for accurate detection
    const trimmedValue = value.trim();

    // Definitive email indicators
    if (trimmedValue.includes('@')) return 'email';

    // Clean value for phone number detection (remove common formatting)
    const cleanValue = trimmedValue.replace(/[\s\-\(\)\.\+]/g, '');

    // Phone number detection: only digits after cleaning
    if (/^\d+$/.test(cleanValue) && cleanValue.length >= 4) {
      return 'phone';
    }

    // Email detection: contains letters (with or without numbers)
    if (/[a-zA-Z]/.test(trimmedValue)) {
      return 'email';
    }

    // Default for anything else
    return 'default';
  };

  // Format phone number for display
  const formatPhoneNumber = (value: string): string => {
    // Remove all non-digit characters
    const digits = value.replace(/\D/g, '');

    // Don't format if too short
    if (digits.length < 4) return digits;

    // Format based on length (US format as example)
    if (digits.length <= 6) {
      return digits.replace(/(\d{3})(\d+)/, '$1-$2');
    } else if (digits.length <= 10) {
      return digits.replace(/(\d{3})(\d{3})(\d+)/, '$1-$2-$3');
    } else {
      return digits.replace(/(\d{3})(\d{3})(\d{4})(\d+)/, '$1-$2-$3-$4');
    }
  };

  // Handle input change with delayed type detection
  const handlePhoneOrEmailChange = (value: string) => {
    setIsUserTyping(true);

    // Always update the input value immediately
    setPhoneOrEmail(value);

    // Clear any existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set a timeout to detect type only after user stops typing
    typingTimeoutRef.current = setTimeout(() => {
      const detectedType = detectInputType(value);

      // Only change input type if it's different and makes sense
      if (detectedType !== inputType) {
        // Apply formatting if switching to phone
        if (detectedType === 'phone') {
          const formattedValue = formatPhoneNumber(value);
          setPhoneOrEmail(formattedValue);
        }
        setInputType(detectedType);
      }

      setIsUserTyping(false);
    }, 500); // Wait 500ms after user stops typing
  };

  // Validation functions
  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

    if (!emailRegex.test(email)) {
      setErrors({ phoneOrEmail: 'Please enter a valid email address' });
      return false;
    }

    setErrors({});
    return true;
  };

  const validatePhoneNumber = (phone: string): boolean => {
    const cleanPhone = phone.replace(/[\s\-\(\)\.\+]/g, '');

    if (!/^\d{4,15}$/.test(cleanPhone)) {
      setErrors({ phoneOrEmail: 'Please enter a valid phone number (4-15 digits)' });
      return false;
    }

    setErrors({});
    return true;
  };

  const validatePhoneOrEmail = (value: string): boolean => {
    const trimmedValue = value.trim();

    if (!trimmedValue) {
      setErrors({ phoneOrEmail: 'Phone number or email is required' });
      return false;
    }

    if (inputType === 'email') {
      return validateEmail(trimmedValue);
    } else if (inputType === 'phone') {
      return validatePhoneNumber(trimmedValue);
    } else {
      return validateEmail(trimmedValue) || validatePhoneNumber(trimmedValue);
    }
  };

  // Handle send OTP
  const handleSendOTP = async () => {
    if (!validatePhoneOrEmail(phoneOrEmail)) return;

    try {
      setIsLoading(true);
      let loginValue = phoneOrEmail.trim();

      if (inputType === 'phone') {
        const cleanPhone = phoneOrEmail.replace(/[\s\-\(\)\.\+]/g, '');
        loginValue = `${countryCode}${cleanPhone}`;
      }

      const response = await login(loginValue);

      if (response.success) {
        // For the new API, we don't get an otpId, just success status
        // Store the login identifier for OTP verification
        localStorage.setItem('temp_login_identifier', loginValue);
        setCurrentStep(LoginStep.OTP_VERIFICATION);
        setCountdown(300);
        setErrors({});
      } else {
        setErrors({ general: response.message });
      }
    } catch (error) {
      setErrors({ general: 'Failed to send OTP. Please try again.' });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle OTP verification
  const handleVerifyOTP = async () => {
    if (!otp.trim()) {
      setErrors({ otp: 'Please enter the OTP' });
      return;
    }

    if (otp.length !== 6) {
      setErrors({ otp: 'OTP must be 6 digits' });
      return;
    }

    try {
      setIsLoading(true);
      const response = await verifyOTP('', otp); // otpId not needed for new API

      if (response.success) {
        setErrors({});
        onClose(); // Close modal on successful login
      } else {
        setErrors({ otp: response.message });
      }
    } catch (error) {
      setErrors({ otp: 'Verification failed. Please try again.' });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle resend OTP
  const handleResendOTP = async () => {
    if (countdown > 0) return;

    try {
      setIsLoading(true);
      let loginValue = phoneOrEmail.trim();

      if (inputType === 'phone') {
        const cleanPhone = phoneOrEmail.replace(/[\s\-\(\)\.\+]/g, '');
        loginValue = `${countryCode}${cleanPhone}`;
      }

      const response = await login(loginValue);

      if (response.success) {
        // Update the stored login identifier for the new OTP
        localStorage.setItem('temp_login_identifier', loginValue);
        setCountdown(300);
        setErrors({});
      } else {
        setErrors({ general: response.message });
      }
    } catch (error) {
      setErrors({ general: 'Failed to resend OTP. Please try again.' });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle social login
  const handleSocialLogin = async (providerId: string) => {
    try {
      setIsLoading(true);
      const response = await socialLogin(providerId);

      if (response.success) {
        onClose(); // Close modal on successful login
      } else {
        setErrors({ general: response.message });
      }
    } catch (error) {
      setErrors({ general: 'Social login failed. Please try again.' });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle keyboard events
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && currentStep === LoginStep.PHONE_EMAIL) {
      e.preventDefault();
      handleSendOTP();
    }
    if (e.key === 'Escape') {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4 relative">
        {/* Close button */}
        <button
          onClick={onClose}
          tabIndex={0}
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 rounded-full p-1"
          aria-label="Close login modal"
        >
          <i className="ri-close-line text-xl"></i>
        </button>

        {/* Header */}
        <div className="text-center mb-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Welcome Back</h2>
          <p className="text-gray-600">Sign in to your account</p>
        </div>

        {/* Phone/Email Step */}
        {currentStep === LoginStep.PHONE_EMAIL && (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {inputType === 'phone' ? 'Phone Number' :
                 inputType === 'email' ? 'Email' :
                 'Phone Number or Email'}
              </label>

              {inputType === 'phone' ? (
                <div className="flex space-x-2">
                  {/* Country Code Selector */}
                  <select
                    value={countryCode}
                    onChange={(e) => setCountryCode(e.target.value)}
                    tabIndex={1}
                    className="px-3 py-2 border text-black border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white min-w-[80px] max-w-[100px] cursor-pointer country-code-select"
                    aria-label="Select country code"
                    disabled={isLoadingCountries}
                    size={1}
                  >
                    {isLoadingCountries ? (
                      <option>Loading...</option>
                    ) : (
                      countryCodes.map((country) => (
                        <option key={country.code} value={country.dial_code}>
                          {country.dial_code}
                        </option>
                      ))
                    )}
                  </select>

                  {/* Phone Number Input */}
                  <input
                    ref={phoneInputRef}
                    type="tel"
                    value={phoneOrEmail}
                    onChange={(e) => handlePhoneOrEmailChange(e.target.value)}
                    onKeyDown={handleKeyDown}
                    placeholder="Enter phone number"
                    tabIndex={2}
                    autoComplete="tel"
                    className="flex-1 px-3 py-2 border text-black border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    aria-label="Phone number"
                  />
                </div>
              ) : inputType === 'email' ? (
                <input
                  ref={emailInputRef}
                  type="email"
                  value={phoneOrEmail}
                  onChange={(e) => handlePhoneOrEmailChange(e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder="Enter email address"
                  tabIndex={1}
                  autoComplete="email"
                  className="w-full px-3 py-2 border text-black border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  aria-label="Email address"
                />
              ) : (
                <input
                  ref={defaultInputRef}
                  type="text"
                  value={phoneOrEmail}
                  onChange={(e) => handlePhoneOrEmailChange(e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder="Enter phone number or email"
                  tabIndex={1}
                  autoComplete="tel email"
                  className="w-full px-3 py-2 border text-black border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  aria-label="Phone number or email"
                />
              )}

              {errors.phoneOrEmail && (
                <p className="text-red-500 text-sm mt-1">{errors.phoneOrEmail}</p>
              )}
            </div>

            {errors.general && (
              <p className="text-red-500 text-sm text-center">{errors.general}</p>
            )}

            <button
              onClick={handleSendOTP}
              disabled={isLoading}
              tabIndex={3}
              className="w-full bg-primary text-white py-2 px-4 rounded-lg hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
            >
              {isLoading ? 'Sending...' : 'Send OTP'}
            </button>

            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white text-gray-500">Or continue with</span>
              </div>
            </div>

            <div className="space-y-2">
              {socialProviders.map((provider, index) => (
                <button
                  key={provider.id}
                  onClick={() => handleSocialLogin(provider.id)}
                  disabled={isLoading}
                  tabIndex={4 + index}
                  className={`w-full flex items-center justify-center space-x-2 py-2 px-4 rounded-lg text-white transition-colors disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-offset-2 ${provider.color}`}
                >
                  <i className={provider.icon}></i>
                  <span>Continue with {provider.name}</span>
                </button>
              ))}
            </div>
          </div>
        )}

        {/* OTP Verification Step */}
        {currentStep === LoginStep.OTP_VERIFICATION && (
          <div className="space-y-4">
            <div className="text-center text-sm text-gray-600 mb-4">
              We've sent a 6-digit code to<br />
              <span className="font-medium">
                {inputType === 'phone'
                  ? `${countryCode}${phoneOrEmail.replace(/[\s\-\(\)\.\+]/g, '')}`
                  : phoneOrEmail.trim()}
              </span>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Enter OTP
              </label>
              <input
                type="text"
                value={otp}
                onChange={(e) => {
                  const value = e.target.value.replace(/\D/g, '').slice(0, 6);
                  setOtp(value);
                  setErrors({});
                }}
                placeholder="Enter 6-digit code"
                maxLength={6}
                className="w-full px-3 py-2 border text-black border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-center text-lg tracking-widest"
                autoComplete="one-time-code"
              />
              {errors.otp && (
                <p className="text-red-500 text-sm mt-1">{errors.otp}</p>
              )}
            </div>

            {errors.general && (
              <p className="text-red-500 text-sm text-center">{errors.general}</p>
            )}

            <button
              onClick={handleVerifyOTP}
              disabled={isLoading || otp.length !== 6}
              className="w-full bg-primary text-white py-2 px-4 rounded-lg hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
            >
              {isLoading ? 'Verifying...' : 'Verify OTP'}
            </button>

            <div className="text-center">
              {countdown > 0 ? (
                <p className="text-sm text-gray-600">
                  Resend OTP in {Math.floor(countdown / 60)}:{(countdown % 60).toString().padStart(2, '0')}
                </p>
              ) : (
                <button
                  onClick={handleResendOTP}
                  disabled={isLoading}
                  className="text-sm text-primary hover:text-primary/80 disabled:opacity-50"
                >
                  Resend OTP
                </button>
              )}
            </div>

            <button
              onClick={() => setCurrentStep(LoginStep.PHONE_EMAIL)}
              className="w-full text-sm text-gray-600 hover:text-gray-800"
            >
              ← Back to login
            </button>
          </div>
        )}
      </div>
    </div>
  );
}