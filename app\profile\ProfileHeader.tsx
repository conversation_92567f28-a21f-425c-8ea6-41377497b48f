
'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '../contexts/AuthContext';
import Image from 'next/image';

export default function ProfileHeader() {
  const { user, updateUser, logout } = useAuth();
  const router = useRouter();
  const [isEditing, setIsEditing] = useState(false);
  const [userInfo, setUserInfo] = useState({
    name: user?.name || 'Guest User',
    email: user?.email || '',
    phone: user?.phone || '',
    dateOfBirth: '1985-03-15',
    gender: 'Male',
    address: '123 Main Street, New York, NY 10001',
    memberSince: 'January 2023',
    totalBookings: 24,
    loyaltyPoints: 1580
  });

  // Update local state when user data changes
  useEffect(() => {
    if (user) {
      setUserInfo(prev => ({
        ...prev,
        name: user.name || 'Guest User',
        email: user.email || '',
        phone: user.phone || ''
      }));
    }
  }, [user]);

  const handleSave = () => {
    setIsEditing(false);
    // Update user data in context
    if (user) {
      updateUser({
        name: userInfo.name,
        email: userInfo.email,
        phone: userInfo.phone
      });
    }
  };

  const handleLogout = () => {
    logout();
    router.push('/');
  };

  return (
    <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
      <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between mb-6">
        <div className="flex items-center space-x-4 mb-4 lg:mb-0">
          <div className="relative">
            {user?.profilePicture ? (
              <Image
                src={user.profilePicture}
                alt="Profile Picture"
                width={80}
                height={80}
                className="w-20 h-20 rounded-full object-cover"
              />
            ) : (
              <div className="w-20 h-20 bg-gradient-to-br from-[#013688] to-[#0a4da1] rounded-full flex items-center justify-center">
                <span className="text-white text-2xl font-bold">
                  {userInfo.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                </span>
              </div>
            )}
            <button className="absolute -bottom-1 -right-1 w-8 h-8 bg-white border-2 border-gray-200 rounded-full flex items-center justify-center hover:bg-gray-50 cursor-pointer">
              <div className="w-4 h-4 flex items-center justify-center">
                <i className="ri-camera-line text-gray-600"></i>
              </div>
            </button>
          </div>
          
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{userInfo.name}</h1>
            <p className="text-gray-600">Premium Member</p>
            <div className="flex items-center space-x-4 mt-2">
              <span className="text-sm text-gray-500">Member since {userInfo.memberSince}</span>
              <div className="flex items-center space-x-1">
                <div className="w-4 h-4 flex items-center justify-center">
                  <i className="ri-star-fill text-yellow-500"></i>
                </div>
                <span className="text-sm text-gray-600">{userInfo.loyaltyPoints} Points</span>
              </div>
            </div>
          </div>
        </div>
        
        <div className="flex space-x-3">
          <button
            onClick={() => isEditing ? handleSave() : setIsEditing(true)}
            className="px-6 py-2 bg-[#013688] text-white rounded-lg hover:bg-[#012458] transition-colors cursor-pointer whitespace-nowrap"
          >
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 flex items-center justify-center">
                <i className={`ri-${isEditing ? 'save' : 'edit'}-line`}></i>
              </div>
              <span>{isEditing ? 'Save Changes' : 'Edit Profile'}</span>
            </div>
          </button>

          <button
            onClick={handleLogout}
            className="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors cursor-pointer whitespace-nowrap"
          >
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 flex items-center justify-center">
                <i className="ri-logout-box-line"></i>
              </div>
              <span>Logout</span>
            </div>
          </button>
        </div>
      </div>

      {/* Profile Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="bg-gradient-to-r from-blue-50 to-blue-100 p-4 rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-blue-600 text-sm font-medium">Total Bookings</p>
              <p className="text-2xl font-bold text-blue-800">{userInfo.totalBookings}</p>
            </div>
            <div className="w-12 h-12 flex items-center justify-center bg-blue-200 rounded-full">
              <i className="ri-plane-line text-blue-600 text-xl"></i>
            </div>
          </div>
        </div>
        
        <div className="bg-gradient-to-r from-green-50 to-green-100 p-4 rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-green-600 text-sm font-medium">Loyalty Points</p>
              <p className="text-2xl font-bold text-green-800">{userInfo.loyaltyPoints}</p>
            </div>
            <div className="w-12 h-12 flex items-center justify-center bg-green-200 rounded-full">
              <i className="ri-star-line text-green-600 text-xl"></i>
            </div>
          </div>
        </div>
        
        <div className="bg-gradient-to-r from-purple-50 to-purple-100 p-4 rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-purple-600 text-sm font-medium">Member Level</p>
              <p className="text-2xl font-bold text-purple-800">Premium</p>
            </div>
            <div className="w-12 h-12 flex items-center justify-center bg-purple-200 rounded-full">
              <i className="ri-vip-crown-line text-purple-600 text-xl"></i>
            </div>
          </div>
        </div>
      </div>

      {/* Profile Details */}
      <div className="border-t border-gray-200 pt-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Personal Information</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
            {isEditing ? (
              <input
                type="text"
                value={userInfo.name}
                onChange={(e) => setUserInfo({...userInfo, name: e.target.value})}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#013688] focus:border-transparent"
              />
            ) : (
              <p className="text-gray-900 py-2">{userInfo.name}</p>
            )}
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
            {isEditing ? (
              <input
                type="email"
                value={userInfo.email}
                onChange={(e) => setUserInfo({...userInfo, email: e.target.value})}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#013688] focus:border-transparent"
              />
            ) : (
              <p className="text-gray-900 py-2">{userInfo.email}</p>
            )}
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
            {isEditing ? (
              <input
                type="tel"
                value={userInfo.phone}
                onChange={(e) => setUserInfo({...userInfo, phone: e.target.value})}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#013688] focus:border-transparent"
              />
            ) : (
              <p className="text-gray-900 py-2">{userInfo.phone}</p>
            )}
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Date of Birth</label>
            {isEditing ? (
              <input
                type="date"
                value={userInfo.dateOfBirth}
                onChange={(e) => setUserInfo({...userInfo, dateOfBirth: e.target.value})}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#013688] focus:border-transparent"
              />
            ) : (
              <p className="text-gray-900 py-2">{new Date(userInfo.dateOfBirth).toLocaleDateString()}</p>
            )}
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Gender</label>
            {isEditing ? (
              <select
                value={userInfo.gender}
                onChange={(e) => setUserInfo({...userInfo, gender: e.target.value})}
                className="w-full px-4 py-2 pr-8 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#013688] focus:border-transparent"
              >
                <option value="Male">Male</option>
                <option value="Female">Female</option>
                <option value="Other">Other</option>
              </select>
            ) : (
              <p className="text-gray-900 py-2">{userInfo.gender}</p>
            )}
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Address</label>
            {isEditing ? (
              <input
                type="text"
                value={userInfo.address}
                onChange={(e) => setUserInfo({...userInfo, address: e.target.value})}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#013688] focus:border-transparent"
              />
            ) : (
              <p className="text-gray-900 py-2">{userInfo.address}</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
