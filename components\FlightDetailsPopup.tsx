'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';

interface FareOption {
  type: string;
  price: number;
  originalPrice?: number;
  features: string[];
  baggage: {
    cabin: string;
    checkin: string;
  };
  flexibility: {
    cancellation: string;
    dateChange: string;
  };
  seats: string;
  meals: string;
  benefits?: string[];
}

interface FlightDetailsPopupProps {
  isOpen: boolean;
  onClose: () => void;
  flight: any;
  searchParams: any;
}

export default function FlightDetailsPopup({ isOpen, onClose, flight, searchParams }: FlightDetailsPopupProps) {
  const router = useRouter();
  const [selectedFareType, setSelectedFareType] = useState('SAVER');

  if (!isOpen) return null;

  // Mock fare options based on the image
  const fareOptions: FareOption[] = [
    {
      type: 'SAVER',
      price: 26317,
      originalPrice: 28500,
      features: ['No refund on Cancellation', 'No refund on Date Change'],
      baggage: {
        cabin: '7 Kg Cabin Baggage',
        checkin: '30 Kgs Check-in Baggage'
      },
      flexibility: {
        cancellation: 'No refund on Cancellation',
        dateChange: 'No refund on Date Change'
      },
      seats: 'Chargeable Seats',
      meals: 'Meals info not available'
    },
    {
      type: 'FLEXI',
      price: 26277,
      originalPrice: 28500,
      features: ['No refund on Cancellation', 'No refund on Date Change'],
      baggage: {
        cabin: '7 Kg Cabin Baggage',
        checkin: '30 Kgs Check-in Baggage'
      },
      flexibility: {
        cancellation: 'No refund on Cancellation',
        dateChange: 'No refund on Date Change'
      },
      seats: 'Chargeable Seats',
      meals: 'Meals info not available',
      benefits: ['Travel Insurance for 5 days']
    },
    {
      type: 'SUPER SAVER',
      price: 26843,
      originalPrice: 29000,
      features: ['No refund on Cancellation', 'No refund on Date Change'],
      baggage: {
        cabin: '7 Kg Cabin Baggage',
        checkin: '30 Kgs Check-in Baggage'
      },
      flexibility: {
        cancellation: 'No refund on Cancellation',
        dateChange: 'No refund on Date Change'
      },
      seats: 'Chargeable Seats',
      meals: 'Meals info not available'
    },
    {
      type: 'PREMIUM',
      price: 29200,
      originalPrice: 32000,
      features: ['Free Cancellation', 'Free Date Change'],
      baggage: {
        cabin: '7 Kg Cabin Baggage',
        checkin: '30 Kgs Check-in Baggage'
      },
      flexibility: {
        cancellation: 'Free Cancellation',
        dateChange: 'Free Date Change'
      },
      seats: 'Complimentary Seats',
      meals: 'Complimentary Meals'
    }
  ];

  const selectedFare = fareOptions.find(fare => fare.type === selectedFareType) || fareOptions[0];

  const handleBookNow = () => {
    const flightBookingData = {
      flight: {
        ...flight,
        price: selectedFare.price,
        fareType: selectedFareType
      },
      bookingDetails: {
        fareType: selectedFareType,
        bookingDate: new Date().toISOString(),
        searchParams: searchParams
      }
    };

    localStorage.setItem('selectedFlightForBooking', JSON.stringify(flightBookingData));

    const bookingParams = new URLSearchParams({
      flightId: flight.id.toString(),
      fareType: selectedFareType.toLowerCase(),
      price: selectedFare.price.toString(),
      from: flight.departure.city,
      to: flight.arrival.city
    });

    router.push(`/flights/booking?${bookingParams.toString()}`);
    onClose();
  };

  const handleLockPrice = () => {
    // Implement lock price functionality
    alert('Price locked for 24 hours!');
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">
            Flight Details and Fare Options available for you!
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 text-2xl"
          >
            ×
          </button>
        </div>

        {/* Flight Info */}
        <div className="p-6 border-b bg-gray-50">
          <div className="flex items-center space-x-4">
            <Image
              src={flight.logo || '/AirlineLogo/6E.png'}
              alt={flight.airline}
              width={32}
              height={32}
              className="rounded"
            />
            <div className="flex-1">
              <div className="font-semibold text-gray-900">
                {flight.departure.city} → {flight.arrival.city}
              </div>
              <div className="text-sm text-gray-600">
                {flight.airline} • {flight.departure.date} • Departure at {flight.departure.time} • Arrival at {flight.arrival.time} (+1 day)
              </div>
            </div>
          </div>
        </div>

        {/* Fare Options */}
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {fareOptions.map((fare, index) => (
              <div
                key={fare.type}
                className={`border rounded-lg p-4 cursor-pointer transition-all ${
                  selectedFareType === fare.type
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => setSelectedFareType(fare.type)}
              >
                {/* Price */}
                <div className="text-center mb-4">
                  <div className="text-2xl font-bold text-gray-900">₹ {fare.price.toLocaleString()}</div>
                  <div className="text-sm text-gray-500">per adult</div>
                  {fare.originalPrice && (
                    <div className="text-xs text-gray-400 line-through">₹ {fare.originalPrice.toLocaleString()}</div>
                  )}
                  <div className="text-sm font-semibold text-blue-600 mt-1">{fare.type}</div>
                </div>

                {/* Baggage */}
                <div className="mb-4">
                  <div className="text-sm font-semibold text-gray-700 mb-2">Baggage</div>
                  <div className="text-xs text-gray-600 space-y-1">
                    <div>✓ {fare.baggage.cabin}</div>
                    <div>✓ {fare.baggage.checkin}</div>
                  </div>
                </div>

                {/* Flexibility */}
                <div className="mb-4">
                  <div className="text-sm font-semibold text-gray-700 mb-2">Flexibility</div>
                  <div className="text-xs text-gray-600 space-y-1">
                    <div>• {fare.flexibility.cancellation}</div>
                    <div>• {fare.flexibility.dateChange}</div>
                  </div>
                </div>

                {/* Seats & Meals */}
                <div className="mb-4">
                  <div className="text-sm font-semibold text-gray-700 mb-2">Seats, Meals & More</div>
                  <div className="text-xs text-gray-600 space-y-1">
                    <div>• {fare.seats}</div>
                    <div>• {fare.meals}</div>
                  </div>
                </div>

                {/* Benefits */}
                {fare.benefits && (
                  <div className="mb-4">
                    <div className="text-sm font-semibold text-orange-600 mb-2">BENEFITS WORTH ₹ 500 INCLUDED</div>
                    <div className="text-xs text-gray-600">
                      {fare.benefits.map((benefit, idx) => (
                        <div key={idx}>🛡️ {benefit}</div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Discount Info */}
                {index === 1 && (
                  <div className="text-xs text-green-600 mb-2">
                    💳 Get ₹ 1,200 additional discount using MMTSPECIAL OR Get Upto 5000 OFF using HDFC Credit cards using HDFC1NTSMI
                  </div>
                )}

                {/* Action Buttons */}
                <div className="space-y-2">
                  <button
                    onClick={handleLockPrice}
                    className="w-full py-2 px-4 border border-blue-500 text-blue-500 rounded text-sm font-semibold hover:bg-blue-50 transition-colors"
                  >
                    LOCK PRICE
                  </button>
                  <button
                    onClick={handleBookNow}
                    className={`w-full py-2 px-4 rounded text-sm font-semibold transition-colors ${
                      selectedFareType === fare.type
                        ? 'bg-orange-500 text-white hover:bg-orange-600'
                        : 'bg-gray-200 text-gray-500'
                    }`}
                  >
                    BOOK NOW
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
